import { create } from 'zustand';

type Location = {
  lat: number;
  lng: number;
  address: string;
};

type SendRequest = {
  id?: string;
  pickup: Location | null;
  dropoff: Location | null;
  receiverName: string;
  receiverPhone: string;
  packageType: string;
  notes: string;
  driverName: string;
  driverPhone: string;
  status: "pending" | "on the way" | "rejected" | "delivered";
};

type SendPackageState = SendRequest & {
  setPickup: (loc: Location) => void;
  setDropoff: (loc: Location) => void;
  updateField: (
    field: keyof Omit<SendPackageState, 'pickup' | 'dropoff' | 'setPickup' | 'setDropoff' | 'updateField' | 'reset'>,
    value: string
  ) => void;
  reset: () => void;
};

type Store = {
  sendRequests: SendRequest[];
  addSendRequest: (sendRequest: SendRequest) => void;
};

export const useSendPackageStore = create<Store>((set) => ({
  sendRequests: [],
  addSendRequest: (sendRequest) =>
    set((state) => ({
      sendRequests: [...state.sendRequests, sendRequest],
    })),
}));

export const useSetSendPackage = create<SendPackageState>((set) => ({
  pickup: null,
  dropoff: null,
  receiverName: '',
  receiverPhone: '',
  packageType: '',
  notes: '',
  driverName: '',
  driverPhone: '',
  status: 'pending',
  setPickup: (pickup) => set({ pickup }),
  setDropoff: (dropoff) => set({ dropoff }),
  updateField: (field, value) => set((state) => ({ ...state, [field]: value })),
  reset: () =>
    set({
      pickup: null,
      dropoff: null,
      receiverName: '',
      receiverPhone: '',
      packageType: '',
      notes: '',
    }),
}));
