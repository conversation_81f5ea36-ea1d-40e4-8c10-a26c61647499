import { useEffect, useState } from 'react';
import { Platform, ScrollView, Dimensions, Linking, TouchableWithoutFeedback, Alert } from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { View, Text, Button, YStack, XStack } from 'tamagui';
import { Ionicons } from '@expo/vector-icons';
import { MotiView } from 'moti';
import { getPackageByTrackingNumber } from '../../../services/apiService';
import MapView, { Marker, Polyline } from 'react-native-maps';

export default function PackageTracking() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();

  const [packageData, setPackageData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch package data from backend
  useEffect(() => {
    const fetchPackageData = async () => {
      if (!id) {
        setError('No tracking number provided');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const data = await getPackageByTrackingNumber(id);
        setPackageData(data);
      } catch (error) {
        console.error('Error fetching package data:', error);
        setError('Failed to load package information');
        Alert.alert('Error', 'Failed to load package information');
      } finally {
        setLoading(false);
      }
    };

    fetchPackageData();
  }, [id]);

  const pickupLoc = packageData?.pickupAddress?.coordinates;
  const dropoffLoc = packageData?.deliveryAddress?.coordinates;

  const customerLocation = {
    latitude: pickupLoc?.lat || 32.2211,
    longitude: pickupLoc?.lng || 35.2544,
  };

  const [driverLocation, setDriverLocation] = useState({
    latitude: customerLocation.latitude - 0.01,
    longitude: customerLocation.longitude - 0.01,
  });

  useEffect(() => {
    const interval = setInterval(() => {
      setDriverLocation((prev) => {
        const latDiff = customerLocation.latitude - prev.latitude;
        const lonDiff = customerLocation.longitude - prev.longitude;
        const step = 0.0003;

        const newLat = prev.latitude + Math.sign(latDiff) * Math.min(Math.abs(latDiff), step);
        const newLon = prev.longitude + Math.sign(lonDiff) * Math.min(Math.abs(lonDiff), step);

        if (Math.abs(latDiff) < 0.0001 && Math.abs(lonDiff) < 0.0001) {
          clearInterval(interval);
          return prev;
        }

        return { latitude: newLat, longitude: newLon };
      });
    }, 1000);
    return () => clearInterval(interval);
  }, []);

  const [eta, setEta] = useState(30 * 60);
  useEffect(() => {
    if (eta <= 0) return;
    const timer = setInterval(() => setEta((e) => e - 1), 1000);
    return () => clearInterval(timer);
  }, [eta]);

  const formatTime = (s: number) => `${Math.floor(s / 60)}m ${s % 60}s`;

  const { height } = Dimensions.get('window');
  const [fullscreen, setFullscreen] = useState(false);

  if (loading) {
    return (
      <View flex={1} justifyContent="center" alignItems="center" bg="$background">
        <Text fontSize="$6" color="$gray10">Loading package information...</Text>
      </View>
    );
  }

  if (error || !packageData) {
    return (
      <View flex={1} justifyContent="center" alignItems="center" bg="$background" padding="$4">
        <Ionicons name="cube-outline" size={64} color="#ccc" />
        <Text fontSize="$6" color="$gray10" textAlign="center" marginTop="$4">
          {error || 'Package not found'}
        </Text>
        <Button
          marginTop="$4"
          onPress={() => router.replace('/home')}
          bg="$primary"
        >
          Back to Home
        </Button>
      </View>
    );
  }

  const statuses = ['Preparing', 'On the Way', 'Delivered'];

  return (
    <>
      {fullscreen && (
        <View style={{ position: 'absolute', top: 0, bottom: 0, left: 0, right: 0, zIndex: 999 }}>
          <MapView
            style={{ flex: 1 }}
            region={{
              latitude: customerLocation.latitude,
              longitude: customerLocation.longitude,
              latitudeDelta: 0.01,
              longitudeDelta: 0.01,
            }}
          >
            <Marker
              coordinate={customerLocation}
              pinColor="green"
              title="Pickup Location"
            />
            <Marker
              coordinate={driverLocation}
              pinColor="purple"
              title="Driver Location"
            />
            <Polyline
              coordinates={[driverLocation, customerLocation]}
              strokeColor="#007AFF"
              strokeWidth={3}
            />
          </MapView>

          <Button
            onPress={() => setFullscreen(false)}
            bg="$primary"
            color="white"
            style={{
              position: 'absolute',
              top: 40,
              right: 20,
              zIndex: 1000,
              padding: 10,
            }}
            br={100}
            hoverStyle={{ bg: '$third' }}
            pressStyle={{ bg: '$third' }}
          >
            <Ionicons name="close" size={20} color="#fff" />
          </Button>
        </View>
      )}

      <ScrollView contentContainerStyle={{ flexGrow: 1, paddingBottom: 120 }}>
        {!fullscreen && (
          <>
            {/* Header */}
            <View
              style={{
                paddingVertical: 30,
                paddingHorizontal: 24,
                borderBottomLeftRadius: 32,
                borderBottomRightRadius: 32,
                backgroundImage: 'linear-gradient(90deg, #7529B3, #8F3DD2)',
                backgroundColor: '#7529B3',
              }}
            >
              <MotiView from={{ opacity: 0, translateY: -10 }} animate={{ opacity: 1, translateY: 0 }}>
                <Text fontSize="$8" fontWeight="700" color="white" textAlign="center">
                  {packageData.deliveryAddress ? '📦 Sent Package' : '📥 Pickup Request'}
                </Text>
              </MotiView>
            </View>

            <YStack p="$4" gap="$4">
              {/* Status Stepper */}
              <XStack justifyContent="space-between" mb="$4" px="$2">
                {statuses.map((status, i) => {
                  const active = i <= 1; // Always show first 2 as active since we have no real status
                  return (
                    <YStack key={status} ai="center">
                      <Ionicons
                        name={
                          status === 'Delivered'
                            ? 'checkmark-circle'
                            : status === 'On the Way'
                            ? 'bicycle'
                            : 'construct-outline'
                        }
                        size={32}
                        color={active ? '#7529B3' : '#ccc'}
                      />
                      <Text mt="$1" color={active ? '#7529B3' : '#ccc'}>
                        {status}
                      </Text>
                    </YStack>
                  );
                })}
              </XStack>

              <Text fontSize="$6">Estimated Arrival: {formatTime(eta)}</Text>

              {packageData.driverName && (
                <XStack justifyContent="space-between">
                  <YStack bg="$gray2" p="$3" br="$6" width="85%">
                    <Text fontWeight="600" fontSize="$5">
                      Driver: {packageData.driverName}
                    </Text>
                  </YStack>
                  <Button
                    icon={<Ionicons name="call" size={20} color="white" />}
                    onPress={() => Linking.openURL(`tel:${packageData.driverPhone}`)}
                    bg="$blue10"
                    circular
                    mt="$1"
                  />
                </XStack>
              )}

              <View
                bg="$fourth"
                style={{
                  padding: 10,
                  borderRadius: 10,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <Text fontSize="$5" color="$primary" fontWeight="600">
                  Tap the map to view and move freely
                </Text>
              </View>

              <TouchableWithoutFeedback onPress={() => setFullscreen(true)}>
                <View style={{ height: height * 0.375, borderRadius: 12, overflow: 'hidden' }}>
                  <MapView
                    style={{ flex: 1 }}
                    initialRegion={{
                      latitude: customerLocation.latitude,
                      longitude: customerLocation.longitude,
                      latitudeDelta: 0.01,
                      longitudeDelta: 0.01,
                    }}
                  >
                    {pickupLoc && (
                      <Marker
                        coordinate={{
                          latitude: pickupLoc.lat ?? 0,
                          longitude: pickupLoc.lng ?? 0
                        }}
                        title="Pickup Location"
                      >
                        <View style={{ width: 30, height: 30, borderRadius: 15, backgroundColor: 'green' }} />
                      </Marker>
                    )}
                    {dropoffLoc && (
                      <Marker
                        coordinate={{
                          latitude: dropoffLoc.lat,
                          longitude: dropoffLoc.lng
                        }}
                        title="Dropoff Location"
                      >
                        <View style={{ width: 30, height: 30, borderRadius: 15, backgroundColor: 'blue' }} />
                      </Marker>
                    )}
                    <Marker
                      coordinate={{
                        latitude: driverLocation.latitude,
                        longitude: driverLocation.longitude
                      }}
                      title="Driver Location"
                    >
                      <View style={{ width: 30, height: 30, borderRadius: 15, backgroundColor: 'purple' }} />
                    </Marker>
                  </MapView>
                </View>
              </TouchableWithoutFeedback>

              <Button
                mt="$4"
                size="$5"
                bg="$primary"
                color="white"
                br="$10"
                onPress={() => router.back()}
                hoverStyle={{ bg: '$third' }}
                pressStyle={{ bg: '$third' }}
              >
                Back
              </Button>
            </YStack>
          </>
        )}
      </ScrollView>
    </>
  );
}
