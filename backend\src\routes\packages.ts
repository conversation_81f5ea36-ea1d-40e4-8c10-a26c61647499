import { Router } from 'express';
import { body, param } from 'express-validator';
import { PackageController } from '../controllers/packageController';
import { authenticate } from '../middleware/auth';

const router = Router();

// Validation rules for creating packages
const createPackageValidation = [
  body('recipientInfo.name')
    .notEmpty()
    .isString()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Recipient name is required and must be between 1 and 100 characters'),
  body('recipientInfo.phone')
    .notEmpty()
    .isString()
    .trim()
    .matches(/^\+?[1-9]\d{1,14}$/)
    .withMessage('Valid recipient phone number is required'),
  body('pickupAddress.street')
    .notEmpty()
    .isString()
    .trim()
    .withMessage('Pickup street address is required'),
  body('pickupAddress.city')
    .notEmpty()
    .isString()
    .trim()
    .withMessage('Pickup city is required'),
  body('pickupAddress.coordinates.lat')
    .isFloat({ min: -90, max: 90 })
    .withMessage('Pickup latitude must be a valid number between -90 and 90'),
  body('pickupAddress.coordinates.lng')
    .isFloat({ min: -180, max: 180 })
    .withMessage('Pickup longitude must be a valid number between -180 and 180'),
  body('deliveryAddress.street')
    .notEmpty()
    .isString()
    .trim()
    .withMessage('Delivery street address is required'),
  body('deliveryAddress.city')
    .notEmpty()
    .isString()
    .trim()
    .withMessage('Delivery city is required'),
  body('deliveryAddress.coordinates.lat')
    .isFloat({ min: -90, max: 90 })
    .withMessage('Delivery latitude must be a valid number between -90 and 90'),
  body('deliveryAddress.coordinates.lng')
    .isFloat({ min: -180, max: 180 })
    .withMessage('Delivery longitude must be a valid number between -180 and 180'),
  body('packageDetails.type')
    .notEmpty()
    .isString()
    .trim()
    .withMessage('Package type is required'),
  body('packageDetails.size')
    .isIn(['small', 'medium', 'large'])
    .withMessage('Package size must be small, medium, or large'),
  body('packageDetails.weight')
    .notEmpty()
    .isString()
    .trim()
    .withMessage('Package weight is required'),
  body('priority')
    .optional()
    .isIn(['standard', 'express', 'urgent'])
    .withMessage('Priority must be standard, express, or urgent'),
  body('paymentMethod')
    .isIn(['cash', 'card', 'wallet'])
    .withMessage('Payment method must be cash, card, or wallet'),
  body('scheduledPickupTime')
    .optional()
    .isISO8601()
    .withMessage('Scheduled pickup time must be a valid date'),
  body('notes')
    .optional()
    .isString()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Notes must be a string with maximum 500 characters')
];

// Validation for pickup requests
const createPickupValidation = [
  body('pickupAddress.street')
    .notEmpty()
    .isString()
    .trim()
    .withMessage('Pickup street address is required'),
  body('pickupAddress.city')
    .notEmpty()
    .isString()
    .trim()
    .withMessage('Pickup city is required'),
  body('pickupAddress.coordinates.lat')
    .isFloat({ min: -90, max: 90 })
    .withMessage('Pickup latitude must be a valid number between -90 and 90'),
  body('pickupAddress.coordinates.lng')
    .isFloat({ min: -180, max: 180 })
    .withMessage('Pickup longitude must be a valid number between -180 and 180'),
  body('packageDetails.type')
    .notEmpty()
    .isString()
    .trim()
    .withMessage('Package type is required'),
  body('packageDetails.size')
    .isIn(['small', 'medium', 'large'])
    .withMessage('Package size must be small, medium, or large'),
  body('packageDetails.weight')
    .notEmpty()
    .isString()
    .trim()
    .withMessage('Package weight is required'),
  body('preferredTime')
    .optional()
    .isString()
    .withMessage('Preferred time must be a string'),
  body('notes')
    .optional()
    .isString()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Notes must be a string with maximum 500 characters')
];

// Validation for updating package status
const updatePackageStatusValidation = [
  param('trackingNumber')
    .notEmpty()
    .isString()
    .withMessage('Tracking number is required'),
  body('status')
    .isIn(['pending', 'confirmed', 'picked_up', 'in_transit', 'out_for_delivery', 'delivered', 'cancelled'])
    .withMessage('Status must be a valid package status'),
  body('trackingUpdate.message')
    .optional()
    .isString()
    .trim()
    .isLength({ max: 200 })
    .withMessage('Tracking message must be a string with maximum 200 characters'),
  body('trackingUpdate.location.lat')
    .optional()
    .isFloat({ min: -90, max: 90 })
    .withMessage('Latitude must be a valid number between -90 and 90'),
  body('trackingUpdate.location.lng')
    .optional()
    .isFloat({ min: -180, max: 180 })
    .withMessage('Longitude must be a valid number between -180 and 180')
];

// All routes require authentication
router.use(authenticate);

// Package routes
router.post('/', createPackageValidation, PackageController.createPackage);
router.post('/request-pickup', createPickupValidation, PackageController.requestPickup);
router.get('/', PackageController.getUserPackages);
router.get('/:trackingNumber', PackageController.getPackageByTrackingNumber);
router.put('/:trackingNumber/status', updatePackageStatusValidation, PackageController.updatePackageStatus);

export default router;
