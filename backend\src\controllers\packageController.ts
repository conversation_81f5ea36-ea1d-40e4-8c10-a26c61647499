import { Request, Response } from 'express';
import { Package } from '../models/Package';
import { validationResult } from 'express-validator';

// Extend Request interface to include user property
interface AuthenticatedRequest extends Request {
  user?: {
    userId: string;
    email: string;
    role: string;
  };
}

export class PackageController {
  // Create a new package delivery request
  static async createPackage(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
        return;
      }

      const userId = req.user?.userId;
      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'User not authenticated'
        });
        return;
      }

      const {
        recipientInfo,
        pickupAddress,
        deliveryAddress,
        packageDetails,
        priority = 'standard',
        paymentMethod,
        scheduledPickupTime,
        notes
      } = req.body;

      // Generate unique tracking number
      const trackingNumber = `PKG-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

      // Calculate cost based on distance and priority
      const baseCost = 15;
      const priorityMultiplier = priority === 'express' ? 1.5 : priority === 'urgent' ? 2 : 1;
      const cost = baseCost * priorityMultiplier;

      const packageDelivery = new Package({
        trackingNumber,
        senderId: userId,
        recipientInfo,
        pickupAddress,
        deliveryAddress,
        packageDetails,
        priority,
        cost,
        paymentMethod,
        scheduledPickupTime,
        notes,
        status: 'pending',
        paymentStatus: 'pending'
      });

      await packageDelivery.save();

      res.status(201).json({
        success: true,
        message: 'Package delivery request created successfully',
        data: packageDelivery
      });
    } catch (error) {
      console.error('Error creating package:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create package delivery request',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Get user's packages
  static async getUserPackages(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'User not authenticated'
        });
        return;
      }

      const { page = 1, limit = 20, status, type } = req.query;
      const pageNum = parseInt(page as string);
      const limitNum = parseInt(limit as string);
      const skip = (pageNum - 1) * limitNum;

      const query: any = { senderId: userId };
      if (status) {
        query.status = status;
      }

      const packages = await Package.find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limitNum)
        .select('-__v');

      const total = await Package.countDocuments(query);

      res.json({
        success: true,
        data: packages,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          pages: Math.ceil(total / limitNum)
        }
      });
    } catch (error) {
      console.error('Error fetching user packages:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch packages',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Get package by tracking number
  static async getPackageByTrackingNumber(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { trackingNumber } = req.params;
      const userId = req.user?.userId;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'User not authenticated'
        });
        return;
      }

      const packageDelivery = await Package.findOne({ 
        $or: [
          { trackingNumber },
          { _id: trackingNumber }
        ],
        senderId: userId 
      }).select('-__v');

      if (!packageDelivery) {
        res.status(404).json({
          success: false,
          message: 'Package not found'
        });
        return;
      }

      res.json({
        success: true,
        data: packageDelivery
      });
    } catch (error) {
      console.error('Error fetching package:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch package',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Update package status
  static async updatePackageStatus(req: Request, res: Response): Promise<void> {
    try {
      const { trackingNumber } = req.params;
      const { status, trackingUpdate } = req.body;

      const packageDelivery = await Package.findOne({ 
        $or: [
          { trackingNumber },
          { _id: trackingNumber }
        ]
      });

      if (!packageDelivery) {
        res.status(404).json({
          success: false,
          message: 'Package not found'
        });
        return;
      }

      packageDelivery.status = status;

      if (trackingUpdate) {
        packageDelivery.trackingUpdates.push({
          status,
          timestamp: new Date(),
          message: trackingUpdate.message,
          location: trackingUpdate.location
        });
      }

      // Update timestamps based on status
      if (status === 'picked_up' && !packageDelivery.actualPickupTime) {
        packageDelivery.actualPickupTime = new Date();
      } else if (status === 'delivered' && !packageDelivery.actualDeliveryTime) {
        packageDelivery.actualDeliveryTime = new Date();
      }

      await packageDelivery.save();

      res.json({
        success: true,
        message: 'Package status updated successfully',
        data: packageDelivery
      });
    } catch (error) {
      console.error('Error updating package status:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update package status',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Request pickup for existing package
  static async requestPickup(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
        return;
      }

      const userId = req.user?.userId;
      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'User not authenticated'
        });
        return;
      }

      const {
        pickupAddress,
        packageDetails,
        preferredTime,
        notes
      } = req.body;

      // Generate unique tracking number for pickup request
      const trackingNumber = `PU-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

      const pickupRequest = new Package({
        trackingNumber,
        senderId: userId,
        pickupAddress,
        packageDetails,
        priority: 'standard',
        cost: 15, // Base pickup cost
        paymentMethod: 'cash',
        scheduledPickupTime: preferredTime === 'asap' ? new Date() : new Date(preferredTime),
        notes,
        status: 'pending',
        paymentStatus: 'pending'
      });

      await pickupRequest.save();

      res.status(201).json({
        success: true,
        message: 'Pickup request created successfully',
        data: pickupRequest
      });
    } catch (error) {
      console.error('Error creating pickup request:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create pickup request',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }
}
