import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { ArrowLeft, Clock, MapPin, Phone, User, Package, CheckCircle, Truck, Navigation } from 'lucide-react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Popup } from 'react-leaflet';
import 'leaflet/dist/leaflet.css';
import L from 'leaflet';
import { getPackageByTrackingNumber } from '../../services/apiService';

// Fix for default markers in react-leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

const PackageTrackingPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const packageId = searchParams.get('packageId');
  const pickupId = searchParams.get('pickupId');
  const trackingNumber = packageId || pickupId;

  const [packageData, setPackageData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch package data from backend
  useEffect(() => {
    const fetchPackageData = async () => {
      if (!trackingNumber) {
        setError('No tracking number provided');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const data = await getPackageByTrackingNumber(trackingNumber);

        // Format the data for the UI
        const formattedData = {
          id: data.trackingNumber,
          type: data.deliveryAddress ? 'sent' : 'pickup',
          fromAddress: data.pickupAddress?.street || 'Pickup Address',
          fromLocation: {
            lat: data.pickupAddress?.coordinates?.lat || 32.2211,
            lng: data.pickupAddress?.coordinates?.lng || 35.2544
          },
          toAddress: data.deliveryAddress?.street || 'Delivery Address',
          toLocation: {
            lat: data.deliveryAddress?.coordinates?.lat || 31.9038,
            lng: data.deliveryAddress?.coordinates?.lng || 35.2034
          },
          senderName: data.senderName || 'Sender',
          senderPhone: data.senderPhone || '',
          receiverName: data.recipientName || 'Recipient',
          receiverPhone: data.recipientPhone || '',
          driverName: data.driverName || 'Driver',
          driverPhone: data.driverPhone || '',
          driverLocation: { lat: 32.1000, lng: 35.1500 }, // Default driver location
          status: data.status,
          estimatedDelivery: data.estimatedDeliveryTime ? new Date(data.estimatedDeliveryTime).toLocaleString() : '2-3 hours',
          packageType: data.packageDetails?.description || 'Package',
          packageSize: data.packageDetails?.dimensions ? 'Custom' : 'Standard',
          packageWeight: data.packageDetails?.weight ? `${data.packageDetails.weight}kg` : 'N/A',
          price: data.cost || 0,
          createdAt: data.createdAt,
          trackingUpdates: data.trackingUpdates || []
        };

        setPackageData(formattedData);
      } catch (error) {
        console.error('Error fetching package data:', error);
        setError('Failed to load package information');
      } finally {
        setLoading(false);
      }
    };

    fetchPackageData();
  }, [trackingNumber]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading package information...</p>
        </div>
      </div>
    );
  }

  if (error || !packageData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Package className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Package Not Found</h2>
          <p className="text-gray-600 mb-4">{error || 'The package you are looking for could not be found.'}</p>
          <button
            onClick={() => navigate('/customer/packages')}
            className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
          >
            Back to Packages
          </button>
        </div>
      </div>
    );
  }

  // Package status timeline from real tracking data
  const statusTimeline = packageData.trackingUpdates.length > 0
    ? packageData.trackingUpdates.map((update: any) => ({
        status: update.status,
        time: new Date(update.timestamp).toLocaleTimeString(),
        completed: true,
        description: update.message
      }))
    : [
        {
          status: packageData.type === 'pickup' ? 'Pickup Requested' : 'Package Sent',
          time: new Date(packageData.createdAt).toLocaleTimeString(),
          completed: true,
          description: packageData.type === 'pickup' ? 'Pickup request has been placed' : 'Package has been sent for delivery'
        },
        {
          status: 'Processing',
          time: new Date(Date.now() - 30 * 60 * 1000).toLocaleTimeString(),
          completed: packageData.status !== 'pending',
          description: 'Your package is being processed'
        },
        {
          status: packageData.type === 'pickup' ? 'Package Picked Up' : 'Package Collected',
          time: new Date(Date.now() - 15 * 60 * 1000).toLocaleTimeString(),
          completed: ['picked_up', 'in_transit', 'out_for_delivery', 'delivered'].includes(packageData.status),
      description: packageData.type === 'pickup' ? 'Package has been picked up from sender' : 'Package has been collected for delivery'
    },
    { 
      status: 'In Transit', 
      time: '10:30 AM', 
      completed: true, 
      description: 'Package is on the way to destination' 
    },
    { 
      status: 'Out for Delivery', 
      time: 'ETA 12:00 PM', 
      completed: false, 
      description: 'Package is out for final delivery' 
    },
    { 
      status: 'Delivered', 
      time: 'ETA 12:30 PM', 
      completed: false, 
      description: 'Package will be delivered to recipient' 
    }
  ];

  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const handleCallDriver = () => {
    window.open(`tel:${packageData.driverPhone}`);
  };

  const handleCallSender = () => {
    window.open(`tel:${packageData.senderPhone}`);
  };

  const handleCallReceiver = () => {
    window.open(`tel:${packageData.receiverPhone}`);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center gap-4">
            <button
              onClick={() => navigate(-1)}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <ArrowLeft className="w-5 h-5 text-gray-600" />
            </button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Track Package</h1>
              <p className="text-gray-600">Package #{packageData.id}</p>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Column - Package Info */}
          <div className="space-y-6">
            {/* Current Status */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center gap-4 mb-4">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                  <Truck className="w-6 h-6 text-blue-600" />
                </div>
                <div>
                  <h2 className="text-lg font-semibold text-gray-900">{packageData.status}</h2>
                  <p className="text-gray-600">ETA: {packageData.estimatedDelivery}</p>
                </div>
              </div>
              
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <p className="text-blue-800 font-medium">
                  Your package is on the way! {packageData.driverName} is delivering it.
                </p>
              </div>
            </div>

            {/* Package Details */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Package Details</h3>
              
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="font-medium text-gray-900">Type</p>
                  <p className="text-gray-600">{packageData.packageType}</p>
                </div>
                <div>
                  <p className="font-medium text-gray-900">Size</p>
                  <p className="text-gray-600">{packageData.packageSize}</p>
                </div>
                <div>
                  <p className="font-medium text-gray-900">Weight</p>
                  <p className="text-gray-600">{packageData.packageWeight}</p>
                </div>
                <div>
                  <p className="font-medium text-gray-900">Service Fee</p>
                  <p className="text-gray-600">₪{packageData.price}</p>
                </div>
              </div>
            </div>

            {/* Contact Information */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Contact Information</h3>
              
              <div className="space-y-4">
                {/* Driver */}
                <div className="flex items-center gap-4">
                  <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                    <User className="w-5 h-5 text-purple-600" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-900">{packageData.driverName}</h4>
                    <p className="text-gray-600 text-sm">Driver</p>
                  </div>
                  <button
                    onClick={handleCallDriver}
                    className="flex items-center gap-2 px-3 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm"
                  >
                    <Phone className="w-4 h-4" />
                    Call
                  </button>
                </div>

                {/* Sender */}
                <div className="flex items-center gap-4">
                  <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                    <User className="w-5 h-5 text-green-600" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-900">{packageData.senderName}</h4>
                    <p className="text-gray-600 text-sm">Sender</p>
                  </div>
                  <button
                    onClick={handleCallSender}
                    className="flex items-center gap-2 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm"
                  >
                    <Phone className="w-4 h-4" />
                    Call
                  </button>
                </div>

                {/* Receiver */}
                <div className="flex items-center gap-4">
                  <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                    <User className="w-5 h-5 text-orange-600" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-900">{packageData.receiverName}</h4>
                    <p className="text-gray-600 text-sm">Receiver</p>
                  </div>
                  <button
                    onClick={handleCallReceiver}
                    className="flex items-center gap-2 px-3 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors text-sm"
                  >
                    <Phone className="w-4 h-4" />
                    Call
                  </button>
                </div>
              </div>
            </div>

            {/* Package Timeline */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Package Timeline</h3>
              
              <div className="space-y-4">
                {statusTimeline.map((step, index) => (
                  <div key={index} className="flex items-start gap-4">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      step.completed 
                        ? 'bg-green-100 text-green-600' 
                        : 'bg-gray-100 text-gray-400'
                    }`}>
                      {step.completed ? (
                        <CheckCircle className="w-5 h-5" />
                      ) : (
                        <Clock className="w-5 h-5" />
                      )}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <h4 className={`font-medium ${
                          step.completed ? 'text-gray-900' : 'text-gray-500'
                        }`}>
                          {step.status}
                        </h4>
                        <span className={`text-sm ${
                          step.completed ? 'text-gray-600' : 'text-gray-400'
                        }`}>
                          {step.time}
                        </span>
                      </div>
                      <p className={`text-sm ${
                        step.completed ? 'text-gray-600' : 'text-gray-400'
                      }`}>
                        {step.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right Column - Map */}
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              <div className="p-4 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900">Live Tracking</h3>
                <p className="text-gray-600">Follow your package's journey in real-time</p>
              </div>
              
              <div className="h-96">
                <MapContainer
                  center={[packageData.driverLocation.lat, packageData.driverLocation.lng]}
                  zoom={10}
                  style={{ height: '100%', width: '100%' }}
                >
                  <TileLayer
                    attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                    url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                  />
                  
                  {/* Pickup Location Marker */}
                  <Marker position={[packageData.fromLocation.lat, packageData.fromLocation.lng]}>
                    <Popup>
                      <div className="text-center">
                        <strong>Pickup Location</strong>
                        <br />
                        <span className="text-sm text-gray-600">{packageData.fromAddress}</span>
                      </div>
                    </Popup>
                  </Marker>
                  
                  {/* Driver Marker */}
                  <Marker position={[packageData.driverLocation.lat, packageData.driverLocation.lng]}>
                    <Popup>
                      <div className="text-center">
                        <strong>{packageData.driverName}</strong>
                        <br />
                        <span className="text-sm text-gray-600">Your Driver</span>
                      </div>
                    </Popup>
                  </Marker>
                  
                  {/* Delivery Location Marker */}
                  <Marker position={[packageData.toLocation.lat, packageData.toLocation.lng]}>
                    <Popup>
                      <div className="text-center">
                        <strong>Delivery Address</strong>
                        <br />
                        <span className="text-sm text-gray-600">{packageData.toAddress}</span>
                      </div>
                    </Popup>
                  </Marker>
                </MapContainer>
              </div>
            </div>

            {/* Addresses */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Addresses</h3>
              
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-1">
                    <div className="w-2 h-2 bg-green-600 rounded-full"></div>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">From</p>
                    <p className="text-gray-600">{packageData.fromAddress}</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center mt-1">
                    <div className="w-2 h-2 bg-red-600 rounded-full"></div>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">To</p>
                    <p className="text-gray-600">{packageData.toAddress}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Map Legend */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
              <h4 className="font-semibold text-gray-900 mb-3">Map Legend</h4>
              <div className="space-y-2 text-sm">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span>Pickup Location</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span>Driver Location (Live)</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <span>Delivery Address</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PackageTrackingPage;
