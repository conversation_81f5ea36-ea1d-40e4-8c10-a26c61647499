import { useState, useEffect } from 'react'
import { Image, ScrollView, Pressable, Alert } from 'react-native'
import { YStack, XStack, Text, H3, Input, Button, Card, Label, Switch, View } from 'tamagui'
import { Ionicons } from '@expo/vector-icons'
import * as Location from 'expo-location';
import { useCurrentUserData } from '~/components/useCurrentUserData'
import { getSupplierById } from '~/services/apiService'
import * as ImagePicker from 'expo-image-picker'
import { Modal, Platform } from 'react-native'
import MapView, { Marker } from 'react-native-maps'
import { MotiView } from 'moti'
import { LinearGradient } from 'expo-linear-gradient'
import DateTimePicker from '@react-native-community/datetimepicker'

export default function SupplierProfile() {
  const { user } = useCurrentUserData();
  const [supplierData, setSupplierData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [email, setEmail] = useState(user?.email || '');
  const [phone, setPhone] = useState('');
  const [storeName, setStoreName] = useState('');
  const [location, setLocation] = useState<[number, number]>([35.2544, 32.2211]); // Default: Nablus, Palestine
  const [bannerUri, setBannerUri] = useState<string | undefined>(undefined);
  const [logoUri, setLogoUri] = useState<string | undefined>(undefined);

  // Fetch supplier data from backend
  useEffect(() => {
    const fetchSupplierData = async () => {
      if (!user?.supplierId) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const supplier = await getSupplierById(user.supplierId);
        setSupplierData(supplier);

        // Update form fields with supplier data
        setPhone(supplier.phone || '');
        setStoreName(supplier.name || '');
        setLocation(supplier.lng && supplier.lat ? [supplier.lng, supplier.lat] : [35.2544, 32.2211]);
        setBannerUri(supplier.banner);
        setLogoUri(supplier.logoUrl);
      } catch (error) {
        console.error('Error fetching supplier data:', error);
        Alert.alert('Error', 'Failed to load supplier information');
      } finally {
        setLoading(false);
      }
    };

    fetchSupplierData();
  }, [user?.supplierId]);

  // Update email when user data changes
  useEffect(() => {
    setEmail(user?.email || '');
  }, [user?.email]);
  
  const [mapFullscreen, setMapFullscreen] = useState(false);
  const [storeOpen, setStoreOpen] = useState(true);
  const [showFullMap, setShowFullMap] = useState(false);

  async function pickBannerImage() {
    const res = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ['images'],
      allowsEditing: true,
      aspect: [16, 9],
      quality: 0.8,
    });
    if (!res.canceled && res.assets[0].uri) setBannerUri(res.assets[0].uri);
  }

  async function pickLogoImage() {
    const res = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ['images'],
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });
    if (!res.canceled && res.assets[0].uri) setLogoUri(res.assets[0].uri);
  }

  const [openTime, setOpenTime] = useState(new Date());
  const [closeTime, setCloseTime] = useState(new Date());
  const [showPicker, setShowPicker] = useState<'open' | 'close' | null>(null);

  const onTimeChange = (_e: any, selected?: Date) => {
    if (selected) {
      if (showPicker === 'open') setOpenTime(selected);
      else if (showPicker === 'close') setCloseTime(selected);
    }
    setShowPicker(null);
  };



  if (loading) {
    return (
      <View flex={1} justifyContent="center" alignItems="center" bg="$background">
        <Text fontSize="$6" color="$gray10">Loading supplier information...</Text>
      </View>
    );
  }

  return (
    <>
      <ScrollView contentContainerStyle={{ paddingBottom: 120 }} showsVerticalScrollIndicator={false}>
        {/* Enhanced Header with Gradient */}
        <MotiView
          from={{ opacity: 0, translateY: -50 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ duration: 800 }}
        >
          <View style={{ position: 'relative' }}>
            <Pressable onPress={pickBannerImage}>
              <LinearGradient
                colors={['#7c3aed', '#a855f7', '#ec4899']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={{ width: '100%', height: 220 }}
              >
                {bannerUri ? (
                  <Image
                    source={{ uri: bannerUri }}
                    style={{ width: '100%', height: 220, opacity: 0.8 }}
                    resizeMode="cover"
                  />
                ) : null}

                {/* Overlay Content */}
                <View
                  style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    backgroundColor: 'rgba(0,0,0,0.3)',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                >
                  <Ionicons name="camera" size={40} color="white" style={{ opacity: 0.8 }} />
                  <Text color="white" fontSize="$4" fontWeight="600" mt="$2">
                    Change Cover Photo
                  </Text>
                </View>

                {/* Store Status Badge */}
                <View
                  style={{
                    position: 'absolute',
                    top: 50,
                    right: 20,
                    backgroundColor: storeOpen ? '#10b981' : '#ef4444',
                    borderRadius: 20,
                    paddingHorizontal: 12,
                    paddingVertical: 6,
                    flexDirection: 'row',
                    alignItems: 'center',
                    gap: 6,
                  }}
                >
                  <View
                    style={{
                      width: 8,
                      height: 8,
                      borderRadius: 4,
                      backgroundColor: 'white',
                    }}
                  />
                  <Text color="white" fontSize="$3" fontWeight="700">
                    {storeOpen ? 'OPEN' : 'CLOSED'}
                  </Text>
                </View>
              </LinearGradient>
            </Pressable>

            {/* Profile Picture with Enhanced Design */}
            <MotiView
              from={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 400, type: 'spring', damping: 15 }}
              style={{
                position: 'absolute',
                bottom: -60,
                left: 0,
                right: 0,
                alignItems: 'center',
              }}
            >
              <Pressable onPress={pickLogoImage}>
                <View
                  style={{
                    width: 120,
                    height: 120,
                    borderRadius: 60,
                    borderWidth: 4,
                    borderColor: 'white',
                    backgroundColor: '#f8fafc',
                    shadowColor: '#000',
                    shadowOffset: { width: 0, height: 8 },
                    shadowOpacity: 0.2,
                    shadowRadius: 16,
                    elevation: 8,
                    position: 'relative',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  {logoUri ? (
                    <Image
                      source={{ uri: logoUri }}
                      style={{
                        width: 112,
                        height: 112,
                        borderRadius: 56,
                        margin: 4,
                      }}
                      resizeMode="cover"
                    />
                  ) : (
                    <View
                      style={{
                        width: 112,
                        height: 112,
                        borderRadius: 56,
                        margin: 4,
                        backgroundColor: '#e2e8f0',
                        justifyContent: 'center',
                        alignItems: 'center',
                      }}
                    >
                      <Ionicons name="storefront" size={40} color="#64748b" />
                    </View>
                  )}

                  {/* Edit Button - Better Positioned */}
                  <View
                    style={{
                      position: 'absolute',
                      bottom: 4,
                      right: 4,
                      backgroundColor: '#7c3aed',
                      borderRadius: 18,
                      width: 36,
                      height: 36,
                      justifyContent: 'center',
                      alignItems: 'center',
                      borderWidth: 3,
                      borderColor: 'white',
                      shadowColor: '#000',
                      shadowOffset: { width: 0, height: 2 },
                      shadowOpacity: 0.2,
                      shadowRadius: 4,
                      elevation: 4,
                    }}
                  >
                    <Ionicons name="camera" size={18} color="white" />
                  </View>
                </View>
              </Pressable>
            </MotiView>
          </View>
        </MotiView>

        {/* Store Name Section with Edit Indication */}
        <MotiView
          from={{ opacity: 0, translateY: 30 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ delay: 600, duration: 600 }}
          style={{ marginTop: 80, paddingHorizontal: 20 }}
        >
          <YStack ai="center" gap="$3">
            <Card bg="$gray1" p="$4" br="$6" borderWidth={2} borderColor="$gray6" width="100%">
              <YStack ai="center" gap="$2">
                <XStack ai="center" gap="$2">
                  <Ionicons name="storefront" size={20} color="#7c3aed" />
                  <Text color="$gray11" fontSize="$3" fontWeight="600">
                    Store Name (Tap to Edit)
                  </Text>
                  <Ionicons name="pencil" size={16} color="#7c3aed" />
                </XStack>

                <Input
                  value={storeName}
                  onChangeText={setStoreName}
                  textAlign="center"
                  fontSize="$6"
                  fontWeight="800"
                  color="$gray12"
                  bg="$gray2"
                  borderWidth={1}
                  borderColor="$gray6"
                  borderRadius="$4"
                  placeholder="Enter your store name"
                  placeholderTextColor="$gray8"
                  width="100%"
                  p="$2"
                />

                <Text color="$gray9" fontSize="$2" textAlign="center">
                  This name will be displayed to customers
                </Text>
              </YStack>
            </Card>

            <XStack ai="center" gap="$2" mt="$2">
              <Ionicons name="location" size={16} color="#64748b" />
              <Text color="$gray10" fontSize="$3">
                Lat: {location[1].toFixed(4)}, Lng: {location[0].toFixed(4)}
              </Text>
            </XStack>
          </YStack>
        </MotiView>

        {/* Enhanced Settings Sections */}
        <YStack gap="$4" p="$4" mt="$4">
          {/* Contact Information */}
          <MotiView
            from={{ opacity: 0, translateX: -30 }}
            animate={{ opacity: 1, translateX: 0 }}
            transition={{ delay: 800, duration: 600 }}
          >
            <Card elevate br="$8" p="$5" bg="$background" borderWidth={1} borderColor="$gray4">
              <YStack gap="$4">
                <XStack ai="center" gap="$3">
                  <View
                    style={{
                      backgroundColor: '#3b82f6',
                      borderRadius: 12,
                      padding: 10,
                    }}
                  >
                    <Ionicons name="person" size={20} color="white" />
                  </View>
                  <H3 color="$gray12" fontWeight="800">Contact Information</H3>
                </XStack>

                <YStack gap="$3">
                  <YStack gap="$2">
                    <Label color="$gray11" fontSize="$3" fontWeight="600">Email Address</Label>
                    <Input
                      value={email}
                      onChangeText={setEmail}
                      autoCapitalize="none"
                      keyboardType="email-address"
                      bg="$gray2"
                      borderColor="$gray6"
                      borderRadius="$4"
                      fontSize="$4"
                      p="$3"
                    />
                  </YStack>

                  <YStack gap="$2">
                    <Label color="$gray11" fontSize="$3" fontWeight="600">Phone Number</Label>
                    <Input
                      value={phone}
                      onChangeText={setPhone}
                      keyboardType="phone-pad"
                      bg="$gray2"
                      borderColor="$gray6"
                      borderRadius="$4"
                      fontSize="$4"
                      p="$3"
                    />
                  </YStack>
                </YStack>
              </YStack>
            </Card>
          </MotiView>

          {/* Business Hours */}
          <MotiView
            from={{ opacity: 0, translateX: 30 }}
            animate={{ opacity: 1, translateX: 0 }}
            transition={{ delay: 1000, duration: 600 }}
          >
            <Card elevate br="$8" p="$5" bg="$background" borderWidth={1} borderColor="$gray4">
              <YStack gap="$4">
                <XStack ai="center" gap="$3">
                  <View
                    style={{
                      backgroundColor: '#10b981',
                      borderRadius: 12,
                      padding: 10,
                    }}
                  >
                    <Ionicons name="time" size={20} color="white" />
                  </View>
                  <H3 color="$gray12" fontWeight="800">Business Hours</H3>
                </XStack>

                <YStack gap="$3">
                  <XStack gap="$3">
                    <YStack gap="$2" flex={1}>
                      <Label color="$gray11" fontSize="$3" fontWeight="600">Opening Time</Label>
                      <Pressable onPress={() => setShowPicker('open')}>
                        <Input
                          editable={false}
                          value={openTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                          bg="$gray2"
                          borderColor="$gray6"
                          borderRadius="$4"
                          fontSize="$4"
                          p="$3"
                        />
                      </Pressable>
                    </YStack>

                    <YStack gap="$2" flex={1}>
                      <Label color="$gray11" fontSize="$3" fontWeight="600">Closing Time</Label>
                      <Pressable onPress={() => setShowPicker('close')}>
                        <Input
                          editable={false}
                          value={closeTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                          bg="$gray2"
                          borderColor="$gray6"
                          borderRadius="$4"
                          fontSize="$4"
                          p="$3"
                        />
                      </Pressable>
                    </YStack>
                  </XStack>

                  {showPicker && (
                    <DateTimePicker
                      value={showPicker === 'open' ? openTime : closeTime}
                      mode="time"
                      display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                      onChange={onTimeChange}
                    />
                  )}

                  <Card bg={storeOpen ? "$green2" : "$red2"} p="$3" br="$4" borderWidth={1} borderColor={storeOpen ? "$green6" : "$red6"}>
                    <XStack ai="center" jc="space-between">
                      <XStack ai="center" gap="$3">
                        <View
                          style={{
                            width: 12,
                            height: 12,
                            borderRadius: 6,
                            backgroundColor: storeOpen ? '#10b981' : '#ef4444',
                          }}
                        />
                        <YStack>
                          <Text color={storeOpen ? "$green11" : "$red11"} fontSize="$4" fontWeight="700">
                            Store is {storeOpen ? 'Open' : 'Closed'}
                          </Text>
                          <Text color={storeOpen ? "$green9" : "$red9"} fontSize="$2">
                            {storeOpen ? 'Accepting orders' : 'Not accepting orders'}
                          </Text>
                        </YStack>
                      </XStack>
                      <Switch
                        checked={storeOpen}
                        onCheckedChange={setStoreOpen}
                        backgroundColor={storeOpen ? '$green8' : '$red7'}
                      />
                    </XStack>
                  </Card>
                </YStack>
              </YStack>
            </Card>
          </MotiView>

          {/* Store Location */}
          <MotiView
            from={{ opacity: 0, translateY: 30 }}
            animate={{ opacity: 1, translateY: 0 }}
            transition={{ delay: 1200, duration: 600 }}
          >
            <Card elevate br="$8" p="$5" bg="$background" borderWidth={1} borderColor="$gray4">
              <YStack gap="$4">
                <XStack ai="center" gap="$3">
                  <View
                    style={{
                      backgroundColor: '#f97316',
                      borderRadius: 12,
                      padding: 10,
                    }}
                  >
                    <Ionicons name="location" size={20} color="white" />
                  </View>
                  <H3 color="$gray12" fontWeight="800">Store Location</H3>
                </XStack>

                <Pressable onPress={() => setMapFullscreen(true)}>
                  <Card
                    elevate
                    br="$6"
                    height={200}
                    overflow="hidden"
                    bg="$gray2"
                    borderWidth={2}
                    borderColor="$gray6"
                    pressStyle={{ scale: 0.98 }}
                  >
                    <View style={{ position: 'relative', flex: 1 }}>
                      {/* Mini Map Preview */}
                      <MapView
                        style={{ flex: 1 }}
                        region={{
                          latitude: location[1],
                          longitude: location[0],
                          latitudeDelta: 0.005,
                          longitudeDelta: 0.005,
                        }}
                        scrollEnabled={false}
                        zoomEnabled={false}
                        pitchEnabled={false}
                        rotateEnabled={false}
                        showsUserLocation={false}
                        showsMyLocationButton={false}
                        showsCompass={false}
                        showsScale={false}
                      >
                        <Marker
                          coordinate={{
                            latitude: location[1],
                            longitude: location[0],
                          }}
                        >
                          <View
                            style={{
                              backgroundColor: '#7c3aed',
                              borderRadius: 15,
                              padding: 6,
                              borderWidth: 2,
                              borderColor: 'white',
                            }}
                          >
                            <Ionicons name="storefront" size={16} color="white" />
                          </View>
                        </Marker>
                      </MapView>

                      {/* Overlay with Tap Instruction */}
                      <LinearGradient
                        colors={['rgba(0,0,0,0.3)', 'rgba(0,0,0,0.6)']}
                        style={{
                          position: 'absolute',
                          bottom: 0,
                          left: 0,
                          right: 0,
                          padding: 12,
                        }}
                      >
                        <XStack ai="center" jc="space-between">
                          <YStack>
                            <Text color="white" fontSize="$4" fontWeight="700">
                              Tap to Edit Location
                            </Text>
                            <Text color="white" fontSize="$2" opacity={0.9}>
                              {location[1].toFixed(4)}, {location[0].toFixed(4)}
                            </Text>
                          </YStack>
                          <View
                            style={{
                              backgroundColor: 'rgba(255,255,255,0.2)',
                              borderRadius: 12,
                              padding: 8,
                            }}
                          >
                            <Ionicons name="expand" size={16} color="white" />
                          </View>
                        </XStack>
                      </LinearGradient>
                    </View>
                  </Card>
                </Pressable>
              </YStack>
            </Card>
          </MotiView>

          {/* Additional Settings */}
          <MotiView
            from={{ opacity: 0, translateY: 30 }}
            animate={{ opacity: 1, translateY: 0 }}
            transition={{ delay: 1400, duration: 600 }}
          >
            <Card elevate br="$8" p="$5" bg="$background" borderWidth={1} borderColor="$gray4">
              <YStack gap="$4">
                <XStack ai="center" gap="$3">
                  <View
                    style={{
                      backgroundColor: '#8b5cf6',
                      borderRadius: 12,
                      padding: 10,
                    }}
                  >
                    <Ionicons name="settings" size={20} color="white" />
                  </View>
                  <H3 color="$gray12" fontWeight="800">Additional Settings</H3>
                </XStack>

                <YStack gap="$3">
                  <Card bg="$gray2" p="$3" br="$4">
                    <XStack ai="center" jc="space-between">
                      <XStack ai="center" gap="$3">
                        <Ionicons name="notifications" size={20} color="#64748b" />
                        <YStack>
                          <Text color="$gray12" fontSize="$4" fontWeight="600">Push Notifications</Text>
                          <Text color="$gray10" fontSize="$2">Receive order alerts</Text>
                        </YStack>
                      </XStack>
                      <Switch checked={true} backgroundColor="$green8" />
                    </XStack>
                  </Card>

                  {/* {(<Card bg="$gray2" p="$3" br="$4">
                    <XStack ai="center" jc="space-between">
                      <XStack ai="center" gap="$3">
                        <Ionicons name="car" size={20} color="#64748b" />
                        <YStack>
                          <Text color="$gray12" fontSize="$4" fontWeight="600">Delivery Service</Text>
                          <Text color="$gray10" fontSize="$2">Offer delivery to customers</Text>
                        </YStack>
                      </XStack>
                      <Switch checked={true} backgroundColor="$green8" />
                    </XStack>
                  </Card>)} */}

                  <Card bg="$gray2" p="$3" br="$4">
                    <XStack ai="center" jc="space-between">
                      <XStack ai="center" gap="$3">
                        <Ionicons name="card" size={20} color="#64748b" />
                        <YStack>
                          <Text color="$gray12" fontSize="$4" fontWeight="600">Online Payments</Text>
                          <Text color="$gray10" fontSize="$2">Accept card payments</Text>
                        </YStack>
                      </XStack>
                      <Switch checked={true} backgroundColor="$green8" />
                    </XStack>
                  </Card>
                </YStack>
              </YStack>
            </Card>
          </MotiView>

          {/* Save Button */}
          <MotiView
            from={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 1600, duration: 600, type: 'spring' }}
          >
            <Button
              size="$5"
              br="$8"
              bg="transparent"
              borderWidth={0}
              pressStyle={{ scale: 0.98 }}
              mt="$4"
            >
              <LinearGradient
                colors={['#7c3aed', '#a855f7']}
                style={{
                  borderRadius: 16,
                  padding: 16,
                  width: '100%',
                  alignItems: 'center',
                  flexDirection: 'row',
                  justifyContent: 'center',
                  gap: 12,
                }}
              >
                <Ionicons name="save" size={24} color="white" />
                <Text color="white" fontSize="$5" fontWeight="800">
                  Save All Changes
                </Text>
              </LinearGradient>
            </Button>
          </MotiView>
        </YStack>
      </ScrollView>

      {/* Enhanced Map Modal */}
      <Modal visible={mapFullscreen} animationType="slide" presentationStyle="fullScreen">
        <View style={{ flex: 1, backgroundColor: '#000' }}>
          {/* Header */}
          <LinearGradient
            colors={['#7c3aed', '#a855f7']}
            style={{
              paddingTop: 50,
              paddingBottom: 20,
              paddingHorizontal: 20,
            }}
          >
            <XStack ai="center" jc="space-between">
              <YStack>
                <Text color="white" fontSize="$5" fontWeight="800">
                  Set Store Location
                </Text>
                <Text color="white" fontSize="$3" opacity={0.9}>
                  Tap on the map to set your store location
                </Text>
              </YStack>
              <Button
                bg="rgba(255,255,255,0.2)"
                color="white"
                br="$10"
                size="$4"
                icon={<Ionicons name="close" size={20} color="white" />}
                onPress={() => setMapFullscreen(false)}
              />
            </XStack>
          </LinearGradient>

          {/* Map */}
          <MapView
            style={{ flex: 1 }}
            initialRegion={{
              latitude: location[1],
              longitude: location[0],
              latitudeDelta: 0.01,
              longitudeDelta: 0.01,
            }}
            onPress={(e) => {
              const { latitude, longitude } = e.nativeEvent.coordinate;
              setLocation([longitude, latitude]);
            }}
            showsUserLocation={true}
            showsMyLocationButton={true}
            showsCompass={true}
            showsScale={true}
          >
            <Marker
              coordinate={{
                latitude: location[1],
                longitude: location[0],
              }}
              title="Store Location"
              description="Your store is located here"
            >
              <View
                style={{
                  backgroundColor: '#7c3aed',
                  borderRadius: 20,
                  padding: 8,
                  borderWidth: 3,
                  borderColor: 'white',
                }}
              >
                <Ionicons name="storefront" size={24} color="white" />
              </View>
            </Marker>
          </MapView>

          {/* Bottom Info Panel */}
          <LinearGradient
            colors={['rgba(0,0,0,0.8)', 'rgba(0,0,0,0.9)']}
            style={{
              position: 'absolute',
              bottom: 0,
              left: 0,
              right: 0,
              padding: 20,
            }}
          >
            <Card bg="rgba(255,255,255,0.1)" p="$4" br="$6" borderWidth={1} borderColor="rgba(255,255,255,0.2)">
              <YStack gap="$3">
                <XStack ai="center" gap="$3">
                  <View
                    style={{
                      backgroundColor: '#7c3aed',
                      borderRadius: 12,
                      padding: 8,
                    }}
                  >
                    <Ionicons name="location" size={20} color="white" />
                  </View>
                  <YStack flex={1}>
                    <Text color="white" fontSize="$4" fontWeight="700">
                      Current Location
                    </Text>
                    <Text color="white" fontSize="$3" opacity={0.8}>
                      Latitude: {location[1].toFixed(6)}
                    </Text>
                    <Text color="white" fontSize="$3" opacity={0.8}>
                      Longitude: {location[0].toFixed(6)}
                    </Text>
                  </YStack>
                </XStack>

                <Button
                  bg="$green9"
                  color="white"
                  br="$6"
                  size="$4"
                  icon={<Ionicons name="checkmark" size={20} color="white" />}
                  onPress={() => setMapFullscreen(false)}
                >
                  <Text color="white" fontSize="$4" fontWeight="700">
                    Confirm Location
                  </Text>
                </Button>
              </YStack>
            </Card>
          </LinearGradient>
        </View>
      </Modal>
    </>
  )
}
