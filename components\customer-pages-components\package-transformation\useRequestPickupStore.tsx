import { create } from 'zustand'

type PickupLocation = {
  address: string;
  lat?: number;
  lng?: number;
}

type RequestPickupSnapshot = {
  id: string;
  pickup: PickupLocation | null;
  itemDescription: string;
  preferredTime: string;
  notes: string;
  driverName: string;
  driverPhone: string;
  status: "pending" | "on the way" | "rejected" | "delivered";
  createdAt: string;
  estimatedTime: string;
  cost: number;
}

type Store = {
  pickupRequests: RequestPickupSnapshot[];
  addPickupRequest: (pickupRequest: RequestPickupSnapshot) => void;
}

export const useRequestPickupStore = create<Store>((set) => ({
  pickupRequests: [],
  addPickupRequest: (pickupRequest) =>
    set((state) => ({
      pickupRequests: [...state.pickupRequests, pickupRequest],
    })),
}));

// Local temporary form store for the form UI
export const useUpdateRequestPickup = create<RequestPickupSnapshot & {
  updateField: <K extends keyof RequestPickupSnapshot>(key: K, value: RequestPickupSnapshot[K]) => void;
  reset: () => void;
}>((set) => ({
  id: '',
  pickup: null,
  itemDescription: '',
  preferredTime: '',
  notes: '',
  driverName: '',
  driverPhone: '',
  status: 'pending',
  createdAt: '',
  estimatedTime: '',
  cost: 0,
  updateField: (key, value) => set({ [key]: value }),
  reset: () =>
    set({
      id: '',
      pickup: null,
      itemDescription: '',
      preferredTime: '',
      notes: '',
      driverName: '',
      driverPhone: '',
      status: 'pending',
      createdAt: '',
      estimatedTime: '',
      cost: 0,
    }),
}));
